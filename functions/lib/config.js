"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CONFIG = void 0;
exports.getEnvironmentConfig = getEnvironmentConfig;
// Configuration constants
exports.CONFIG = {
    // File size limits (in bytes)
    MAX_FILE_SIZE: {
        "farm-images": parseInt(process.env.MAX_FARM_IMAGE_SIZE_MB || "50") * 1024 * 1024,
        "profile-images": parseInt(process.env.MAX_PROFILE_IMAGE_SIZE_MB || "10") * 1024 * 1024,
    },
    // Image processing settings
    IMAGE_QUALITY: parseInt(process.env.IMAGE_QUALITY || "85"),
    MAX_DIMENSIONS: {
        "farm-images": parseInt(process.env.MAX_IMAGE_DIMENSION_FARM || "2048"),
        "profile-images": parseInt(process.env.MAX_IMAGE_DIMENSION_PROFILE || "512"),
    },
    // Allowed MIME types
    ALLOWED_MIME_TYPES: [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/webp",
        "image/gif"
    ],
    // CORS settings
    CORS: {
        ALLOWED_ORIGINS: [
            "http://localhost:3000",
            "https://localhost:3000",
            "*", // Allow all origins for development - remove in production
            // Add your production domains here
        ],
        ALLOWED_METHODS: ["POST", "OPTIONS", "GET"],
        ALLOWED_HEADERS: ["Content-Type", "Authorization", "Accept", "Origin", "X-Requested-With"],
    }
};
// Helper function to get environment-specific config
function getEnvironmentConfig() {
    const isDevelopment = process.env.NODE_ENV === "development";
    return {
        isDevelopment,
        projectId: process.env.GCLOUD_PROJECT || process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    };
}
//# sourceMappingURL=config.js.map