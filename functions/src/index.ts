import {onRequest} from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import {imageUploadHandler} from "./imageUpload";

// Initialize Firebase Admin SDK
admin.initializeApp();

// Simple health check function for testing
export const healthCheck = onRequest({
  region: "us-central1",
  timeoutSeconds: 60,
  memory: "256MiB",
  cors: true,
}, (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url
  });
});

// Export the image upload function
export const uploadImage = onRequest({
  region: "us-central1",
  timeoutSeconds: 540,
  memory: "1GiB",
  cors: true,
}, imageUploadHandler);
