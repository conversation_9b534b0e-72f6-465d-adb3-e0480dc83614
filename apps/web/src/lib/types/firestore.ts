import { Timestamp } from 'firebase/firestore'
import { SouthAfricanProvince } from '@/lib/constants'

export type UserRole = 'farm_owner' | 'guest'
export type BookingStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed'
export type ActivityType = 'hunting' | 'photo_safari' | 'both'

// Base interface for Firestore documents
export interface FirestoreDocument {
  id: string
  createdAt: Timestamp
  updatedAt: Timestamp
}

// User Profile (stored in users collection)
export interface UserProfile extends FirestoreDocument {
  email: string
  fullName: string
  firstName: string
  lastName: string
  phone?: string
  role: UserRole
  bio?: string
  profileImageUrl?: string
  languagePreference: string
}

// Game Farm (stored in farms collection)
export interface GameFarm extends FirestoreDocument {
  ownerId: string
  name: string
  description?: string
  descriptionAfrikaans?: string
  location: string
  province: SouthAfricanProvince
  coordinates?: string
  sizeHectares?: number
  activityTypes: ActivityType
  contactEmail: string
  contactPhone?: string
  websiteUrl?: string
  rules?: string
  rulesAfrikaans?: string
  pricingInfo?: string
  pricePerDay?: number
  isActive: boolean
  featured: boolean
}

// Farm Amenity (stored in amenities collection)
export interface FarmAmenity extends FirestoreDocument {
  name: string
  nameAfrikaans?: string
  icon?: string
  description?: string
}

// Accommodation Type (stored in accommodationTypes collection)
export interface AccommodationType extends FirestoreDocument {
  name: string
  nameAfrikaans?: string
  description?: string
}

// Farm Accommodation (stored as subcollection under farms)
export interface FarmAccommodation extends FirestoreDocument {
  farmId: string
  accommodationTypeId: string
  name: string
  description?: string
  capacity: number
  pricePerNight?: number
  available: boolean
  accommodationType?: AccommodationType
}

// Game Species (stored in species collection)
export interface GameSpecies extends FirestoreDocument {
  name: string
  nameAfrikaans?: string
  scientificName?: string
  category?: string
  description?: string
  imageUrl?: string
}

// Farm Species (stored as subcollection under farms)
export interface FarmSpecies {
  farmId: string
  speciesId: string
  availableForHunting: boolean
  availableForViewing: boolean
  estimatedPopulation?: number
  trophyFee?: number
  species?: GameSpecies
}

// Farm Image (stored as subcollection under farms)
export interface FarmImage extends FirestoreDocument {
  farmId: string
  imageUrl: string
  altText?: string
  displayOrder: number
  isPrimary: boolean
}

// Availability (stored as subcollection under farms)
export interface Availability extends FirestoreDocument {
  farmId: string
  date: string // ISO date string
  availableSlots: number
  minDurationDays: number
  maxDurationDays: number
  basePrice?: number
  notes?: string
}

// Booking (stored in bookings collection)
export interface Booking extends FirestoreDocument {
  farmId: string
  hunterId: string
  startDate: string // ISO date string
  endDate: string // ISO date string
  durationDays: number
  status: BookingStatus
  activityType: ActivityType
  groupSize: number
  accommodationId?: string
  specialRequests?: string
  totalPrice?: number
  bookingReference: string
  confirmedAt?: Timestamp
  cancelledAt?: Timestamp
  cancellationReason?: string
}

// Review (stored as subcollection under farms)
export interface Review extends FirestoreDocument {
  bookingId: string
  reviewerId: string
  farmId: string
  rating: number // 1-5
  title?: string
  comment?: string
  wouldRecommend?: boolean
  isPublic: boolean
  responseFromOwner?: string
  respondedAt?: Timestamp
}

// Message (stored as subcollection under bookings)
export interface Message extends FirestoreDocument {
  bookingId: string
  senderId: string
  recipientId: string
  message: string
  readAt?: Timestamp
}

// Complex view types for UI components
export interface FarmWithDetails extends GameFarm {
  amenities?: FarmAmenity[]
  accommodations?: FarmAccommodation[]
  species?: FarmSpecies[]
  images?: FarmImage[]
  reviews?: Review[]
  averageRating?: number
  reviewCount?: number
}

export interface BookingWithDetails extends Booking {
  farm?: GameFarm
  hunter?: UserProfile
  accommodation?: FarmAccommodation
  messages?: Message[]
}

// Helper types for form data
export interface CreateFarmData {
  name: string
  description?: string
  descriptionAfrikaans?: string
  location: string
  province: SouthAfricanProvince
  coordinates?: string
  sizeHectares?: number
  activityTypes: ActivityType
  contactEmail: string
  contactPhone?: string
  websiteUrl?: string
  rules?: string
  rulesAfrikaans?: string
  pricingInfo?: string
}

export interface CreateBookingData {
  farmId: string
  startDate: string
  endDate: string
  activityType: ActivityType
  groupSize: number
  accommodationId?: string
  specialRequests?: string
}

export interface CreateReviewData {
  bookingId: string
  farmId: string
  rating: number
  title?: string
  comment?: string
  wouldRecommend?: boolean
  isPublic: boolean
}
