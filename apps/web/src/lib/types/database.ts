import { SouthAfricanProvince } from '@/lib/constants'

export type UserRole = 'hunter' | 'farm_owner' | 'photo_safari_guest' | 'admin'
export type BookingStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed'
export type ActivityType = 'hunting' | 'photo_safari' | 'both'

export interface Profile {
  id: string
  email: string
  full_name: string
  phone?: string
  role: UserRole
  bio?: string
  profile_image_url?: string
  language_preference: string
  created_at: string
  updated_at: string
}

export interface GameFarm {
  id: string
  owner_id: string
  name: string
  description?: string
  description_afrikaans?: string
  location: string
  province: SouthAfricanProvince
  coordinates?: string
  size_hectares?: number
  activity_types: ActivityType
  contact_email: string
  contact_phone?: string
  website_url?: string
  rules?: string
  rules_afrikaans?: string
  pricing_info?: string
  is_active: boolean
  featured: boolean
  created_at: string
  updated_at: string
}

export interface FarmAmenity {
  id: string
  name: string
  name_afrikaans?: string
  icon?: string
  description?: string
  created_at: string
}

export interface AccommodationType {
  id: string
  name: string
  name_afrikaans?: string
  description?: string
  created_at: string
}

export interface FarmAccommodation {
  id: string
  farm_id: string
  accommodation_type_id: string
  name: string
  description?: string
  capacity: number
  price_per_night?: number
  available: boolean
  created_at: string
  updated_at: string
  accommodation_type?: AccommodationType
}

export interface GameSpecies {
  id: string
  name: string
  name_afrikaans?: string
  scientific_name?: string
  category?: string
  description?: string
  image_url?: string
  created_at: string
}

export interface FarmSpecies {
  farm_id: string
  species_id: string
  available_for_hunting: boolean
  available_for_viewing: boolean
  estimated_population?: number
  trophy_fee?: number
  species?: GameSpecies
}

export interface FarmImage {
  id: string
  farm_id: string
  image_url: string
  alt_text?: string
  display_order: number
  is_primary: boolean
  created_at: string
}

export interface Availability {
  id: string
  farm_id: string
  date: string
  available_slots: number
  min_duration_days: number
  max_duration_days: number
  base_price?: number
  notes?: string
  created_at: string
}

export interface Booking {
  id: string
  farm_id: string
  hunter_id: string
  start_date: string
  end_date: string
  duration_days: number
  status: BookingStatus
  activity_type: ActivityType
  group_size: number
  accommodation_id?: string
  special_requests?: string
  total_price?: number
  booking_reference?: string
  confirmed_at?: string
  cancelled_at?: string
  cancellation_reason?: string
  created_at: string
  updated_at: string
  farm?: GameFarm
  hunter?: Profile
  accommodation?: FarmAccommodation
}

export interface Review {
  id: string
  booking_id: string
  reviewer_id: string
  farm_id: string
  rating: number
  title?: string
  comment?: string
  would_recommend?: boolean
  is_public: boolean
  response_from_owner?: string
  responded_at?: string
  created_at: string
  updated_at: string
  reviewer?: Profile
  farm?: GameFarm
}

export interface Message {
  id: string
  booking_id: string
  sender_id: string
  recipient_id: string
  message: string
  read_at?: string
  created_at: string
  sender?: Profile
  recipient?: Profile
}

// Database view types for complex queries
export interface FarmWithDetails extends GameFarm {
  amenities?: FarmAmenity[]
  accommodations?: FarmAccommodation[]
  species?: FarmSpecies[]
  images?: FarmImage[]
  reviews?: Review[]
  average_rating?: number
  review_count?: number
}

export interface BookingWithDetails extends Booking {
  farm?: GameFarm
  hunter?: Profile
  accommodation?: FarmAccommodation
  messages?: Message[]
}