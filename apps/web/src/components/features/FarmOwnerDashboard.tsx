'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { farmService, bookingService } from '@/lib/firebase/firestore'
import { GameFarm, Booking } from '@/lib/types/firestore'
import { Plus, MapPin, Calendar, DollarSign, Users, Eye, Edit, BarChart3, Star, Trash2 } from 'lucide-react'
import Link from 'next/link'

export function FarmOwnerDashboard() {
  const { userProfile } = useAuth()
  const [farms, setFarms] = useState<GameFarm[]>([])
  const [bookings, setBookings] = useState<Booking[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [deletingFarmId, setDeletingFarmId] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      if (!userProfile?.id) return

      try {
        setLoading(true)
        setError(null)

        // Fetch user's farms
        const userFarms = await farmService.getByOwner(userProfile.id)
        setFarms(userFarms)

        // Fetch bookings for user's farms
        const farmIds = userFarms.map(farm => farm.id)
        if (farmIds.length > 0) {
          const allBookings = await bookingService.getAll()
          const farmBookings = allBookings.filter(booking => 
            farmIds.includes(booking.farmId)
          )
          setBookings(farmBookings)
        }
      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError('Failed to load dashboard data')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [userProfile?.id])

  const handleDeleteFarm = async (farmId: string, farmName: string) => {
    if (!confirm(`Are you sure you want to delete "${farmName}"? This action cannot be undone.`)) {
      return
    }

    try {
      setDeletingFarmId(farmId)
      await farmService.delete(farmId)

      // Remove the farm from the local state
      setFarms(prevFarms => prevFarms.filter(farm => farm.id !== farmId))

      // Also remove any bookings for this farm
      setBookings(prevBookings => prevBookings.filter(booking => booking.farmId !== farmId))
    } catch (err) {
      console.error('Error deleting farm:', err)
      setError('Failed to delete farm. Please try again.')
    } finally {
      setDeletingFarmId(null)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  // Calculate statistics
  const totalBookings = bookings.length
  const pendingBookings = bookings.filter(b => b.status === 'pending').length
  const totalRevenue = bookings
    .filter(b => b.status === 'confirmed')
    .reduce((sum, b) => sum + (b.totalPrice || 0), 0)

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-earth-900">
              Welcome back, {userProfile?.firstName || 'Farm Owner'}!
            </h1>
            <p className="text-earth-600 mt-2">
              Manage your farms and bookings from your dashboard
            </p>
          </div>
          <Link href="/farms/create">
            <Button variant="primary" className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add New Farm
            </Button>
          </Link>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-earth-600 text-sm font-medium">Total Farms</p>
              <p className="text-2xl font-bold text-earth-900">{farms.length}</p>
            </div>
            <MapPin className="w-8 h-8 text-accent-600" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-earth-600 text-sm font-medium">Total Bookings</p>
              <p className="text-2xl font-bold text-earth-900">{totalBookings}</p>
            </div>
            <Calendar className="w-8 h-8 text-accent-600" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-earth-600 text-sm font-medium">Pending Requests</p>
              <p className="text-2xl font-bold text-earth-900">{pendingBookings}</p>
            </div>
            <Users className="w-8 h-8 text-accent-600" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-earth-600 text-sm font-medium">Total Revenue</p>
              <p className="text-2xl font-bold text-earth-900">
                R{totalRevenue.toLocaleString()}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-accent-600" />
          </div>
        </Card>
      </div>

      {/* My Farms Section */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-semibold text-earth-900">My Farms</h2>
          <Link href="/farms/create">
            <Button variant="primary" className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add New Farm
            </Button>
          </Link>
        </div>

        {farms.length === 0 ? (
          <Card className="p-8">
            <div className="text-center">
              <MapPin className="w-16 h-16 text-earth-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-earth-900 mb-2">
                No farms yet
              </h3>
              <p className="text-earth-600 mb-6">
                Create your first farm listing to start receiving bookings from safari enthusiasts.
              </p>
              <Link href="/farms/create">
                <Button variant="primary" className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  Create Your First Farm
                </Button>
              </Link>
            </div>
          </Card>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {farms.slice(0, 9).map((farm) => (
                <Card key={farm.id} className="overflow-hidden">
                  <div className="p-0">
                    {/* Farm Image Placeholder */}
                    <div className="h-48 bg-gradient-to-br from-earth-200 to-earth-300 flex items-center justify-center">
                      <MapPin className="w-12 h-12 text-earth-600" />
                    </div>

                    <div className="p-6">
                      {/* Farm Header */}
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="text-lg font-semibold text-earth-900 mb-1">
                            {farm.name}
                          </h3>
                          <p className="text-sm text-earth-600 flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            {farm.location}, {farm.province}
                          </p>
                        </div>
                        <div className="flex items-center gap-1">
                          <div className={`w-2 h-2 rounded-full ${farm.isActive ? 'bg-green-500' : 'bg-red-500'}`} />
                          <span className="text-xs text-earth-600">
                            {farm.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </div>

                      {/* Farm Details */}
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-earth-600">Activity Type:</span>
                          <span className="font-medium capitalize text-earth-900">
                            {farm.activityTypes.replace('_', ' ')}
                          </span>
                        </div>
                        {farm.sizeHectares && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-earth-600">Size:</span>
                            <span className="font-medium text-earth-900">
                              {farm.sizeHectares.toLocaleString()} hectares
                            </span>
                          </div>
                        )}
                        {farm.pricePerDay && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-earth-600">Price:</span>
                            <span className="font-medium text-earth-900">
                              R{farm.pricePerDay.toLocaleString()}/day
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="grid grid-cols-2 gap-2 mb-2">
                        <Link href={`/farms/${farm.id}`}>
                          <Button variant="outline" size="sm" className="w-full flex items-center gap-1">
                            <Eye className="w-3 h-3" />
                            View
                          </Button>
                        </Link>
                        <Link href={`/farms/${farm.id}/edit`}>
                          <Button variant="outline" size="sm" className="w-full flex items-center gap-1">
                            <Edit className="w-3 h-3" />
                            Edit
                          </Button>
                        </Link>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <Link href={`/farms/${farm.id}/analytics`}>
                          <Button variant="primary" size="sm" className="w-full flex items-center gap-1">
                            <BarChart3 className="w-3 h-3" />
                            Analytics
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteFarm(farm.id, farm.name)}
                          disabled={deletingFarmId === farm.id}
                        >
                          <Trash2 className="w-3 h-3" />
                          {deletingFarmId === farm.id ? 'Deleting...' : 'Delete'}
                        </Button>
                      </div>

                      {/* Quick Stats */}
                      <div className="mt-4 pt-4 border-t border-earth-200">
                        <div className="grid grid-cols-2 gap-4 text-center">
                          <div>
                            <div className="flex items-center justify-center gap-1 text-earth-600 mb-1">
                              <Users className="w-3 h-3" />
                              <span className="text-xs">Bookings</span>
                            </div>
                            <p className="text-sm font-semibold text-earth-900">
                              {bookings.filter(b => b.farmId === farm.id).length}
                            </p>
                          </div>
                          <div>
                            <div className="flex items-center justify-center gap-1 text-earth-600 mb-1">
                              <Star className="w-3 h-3" />
                              <span className="text-xs">Rating</span>
                            </div>
                            <p className="text-sm font-semibold text-earth-900">N/A</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {farms.length > 9 && (
              <div className="text-center mt-6">
                <Link href="/dashboard/farms">
                  <Button variant="outline">
                    View All {farms.length} Farms
                  </Button>
                </Link>
              </div>
            )}
          </>
        )}
      </div>

      {/* Recent Booking Requests */}
      <div>
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-earth-900">Recent Booking Requests</h2>
            <Link href="/dashboard/bookings">
              <Button variant="outline" size="sm">
                View All
              </Button>
            </Link>
          </div>

          {bookings.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-earth-400 mx-auto mb-4" />
              <p className="text-earth-600">No booking requests yet</p>
            </div>
          ) : (
            <div className="space-y-4">
              {bookings.slice(0, 3).map((booking) => {
                const farm = farms.find(f => f.id === booking.farmId)
                return (
                  <div key={booking.id} className="flex items-center justify-between p-4 bg-earth-50 rounded-lg">
                    <div>
                      <h3 className="font-medium text-earth-900">{farm?.name || 'Unknown Farm'}</h3>
                      <p className="text-sm text-earth-600">
                        {new Date(booking.startDate).toLocaleDateString()} - {new Date(booking.endDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {booking.status}
                      </span>
                      <p className="text-sm font-medium text-earth-900 mt-1">
                        R{booking.totalPrice?.toLocaleString()}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </Card>
      </div>
    </div>
  )
}
