'use client'

import { useAuth } from '@/hooks/useAuth'
import { FarmOwnerDashboard } from './FarmOwnerDashboard'
import { GuestDashboard } from './GuestDashboard'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

export function UnifiedDashboard() {
  const { userProfile, loading } = useAuth()

  if (loading || !userProfile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  // Render dashboard based on user role
  if (userProfile.role === 'farm_owner') {
    return <FarmOwnerDashboard />
  }

  // Default to guest dashboard for all other roles
  return <GuestDashboard />
}
